{"name": "module-alias", "description": "Create aliases of directories and register custom module paths", "version": "2.2.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"test": "npm run lint && npm run testonly", "testonly": "NODE_ENV=test mocha test/specs.js", "testonly-watch": "NODE_ENV=test mocha -w test/specs.js", "lint": "standard"}, "bugs": {"url": "https://github.com/ilearnio/module-alias/issues"}, "homepage": "https://github.com/ilearnio/module-alias", "keywords": ["extend", "modules", "node", "path", "resolve"], "license": "MIT", "main": "index.js", "files": ["index.js", "register.js", "README.md", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/ilearnio/module-alias.git"}, "devDependencies": {"chai": "^3.5.0", "hello-world-classic": "ilearnio/hello-world-classic", "husky": "^3.0.2", "mocha": "^2.4.5", "semver": "^6.1.1", "standard": "^12.0.1"}, "husky": {"hooks": {"pre-push": "npm run test"}}}