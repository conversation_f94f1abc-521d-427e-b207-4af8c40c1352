{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "projectFolder": ".", "mainEntryPointFilePath": "../../temp/packages/compiler-sfc/src/index.d.ts", "compiler": {"tsconfigFilePath": "../../api-extractor.tsconfig.json"}, "dtsRollup": {"enabled": true, "untrimmedFilePath": "", "publicTrimmedFilePath": "./dist/compiler-sfc.d.ts"}, "apiReport": {"enabled": false}, "docModel": {"enabled": false}, "tsdocMetadata": {"enabled": false}, "messages": {"compilerMessageReporting": {"default": {"logLevel": "warning"}}, "extractorMessageReporting": {"default": {"logLevel": "warning", "addToApiReportFile": true}, "ae-missing-release-tag": {"logLevel": "none"}, "ae-internal-missing-underscore": {"logLevel": "none"}, "ae-forgotten-export": {"logLevel": "none"}}, "tsdocMessageReporting": {"default": {"logLevel": "warning"}, "tsdoc-undefined-tag": {"logLevel": "none"}}}}