{"name": "wrap-ansi", "version": "3.0.1", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": "chalk/wrap-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0"}, "devDependencies": {"ava": "^0.21.0", "chalk": "^2.0.1", "coveralls": "^2.11.4", "has-ansi": "^3.0.0", "nyc": "^11.0.3", "strip-ansi": "^4.0.0", "xo": "^0.18.2"}}