{"name": "mqtt", "description": "A library for the MQTT protocol", "version": "4.3.8", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/scarry1992)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/YoDaMa)"], "keywords": ["mqtt", "publish/subscribe", "publish", "subscribe"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/mqttjs/MQTT.js.git"}, "main": "mqtt.js", "types": "types/index.d.ts", "scripts": {"pretest": "standard | snazzy", "typescript-compile-test": "tsc -p test/typescript/tsconfig.json", "typescript-compile-execute": "node test/typescript/broker-connect-subscribe-and-publish.js", "browser-build": "rimraf dist/ && mkdirp dist/ && browserify mqtt.js --standalone mqtt > dist/mqtt.js && uglifyjs dist/mqtt.js --compress --mangle --output dist/mqtt.min.js", "prepare": "npm run browser-build", "unit-test:node": "node_modules/.bin/nyc --reporter=lcov --reporter=text ./node_modules/mocha/bin/_mocha --exit", "unit-test:browser": "airtap --server test/browser/server.js test/browser/test.js", "test:node": "npm run unit-test:node && codecov", "test:browser": "npm run browser-build && npm run unit-test:browser", "test:typescript": "npm run typescript-compile-test && npm run typescript-compile-execute", "test": "npm run test:node && npm run test:typescript"}, "pre-commit": ["pretest"], "bin": {"mqtt_pub": "./bin/pub.js", "mqtt_sub": "./bin/sub.js", "mqtt": "./bin/mqtt.js"}, "files": ["dist/", "CONTRIBUTING.md", "doc", "lib", "bin", "types", "mqtt.js"], "engines": {"node": ">=10.0.0"}, "browser": {"./mqtt.js": "./lib/connect/index.js", "fs": false, "tls": false, "net": false}, "dependencies": {"commist": "^1.0.0", "concat-stream": "^2.0.0", "debug": "^4.1.1", "duplexify": "^4.1.1", "help-me": "^3.0.0", "inherits": "^2.0.3", "lru-cache": "^6.0.0", "minimist": "^1.2.5", "mqtt-packet": "^6.8.0", "number-allocator": "^1.0.9", "pump": "^3.0.0", "readable-stream": "^3.6.0", "reinterval": "^1.1.0", "rfdc": "^1.3.0", "split2": "^3.1.0", "ws": "^7.5.5", "xtend": "^4.0.2"}, "devDependencies": {"@types/node": "^10.0.0", "@types/tape": "^4.13.2", "@types/ws": "^7.4.7", "aedes": "^0.46.2", "airtap": "^4.0.4", "browserify": "^16.5.0", "chai": "^4.2.0", "chokidar": "^3.5.3", "codecov": "^3.0.4", "end-of-stream": "^1.4.1", "global": "^4.3.2", "mkdirp": "^0.5.1", "mocha": "^9.2.0", "mqtt-connection": "^4.0.0", "nyc": "^15.0.1", "pre-commit": "^1.2.2", "rimraf": "^3.0.2", "should": "^13.2.1", "sinon": "^9.0.0", "snazzy": "^9.0.0", "standard": "^16.0.4", "tape": "^5.5.2", "typescript": "^4.5.5", "uglify-es": "^3.3.9"}, "standard": {"env": ["mocha"]}}