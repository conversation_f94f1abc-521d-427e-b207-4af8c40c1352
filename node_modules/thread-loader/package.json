{"name": "thread-loader", "version": "3.0.4", "description": "Runs the following loaders in a worker pool", "license": "MIT", "repository": "webpack-contrib/thread-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/thread-loader", "bugs": "https://github.com/webpack-contrib/thread-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest --forceExit", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.27.0 || ^5.0.0"}, "dependencies": {"json-parse-better-errors": "^1.0.2", "loader-runner": "^4.1.0", "loader-utils": "^2.0.0", "neo-async": "^2.6.2", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/preset-env": "^7.12.1", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.1", "babel-loader": "^8.1.0", "cross-env": "^7.0.2", "css-loader": "^5.0.0", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.12.1", "eslint-config-prettier": "^6.14.0", "eslint-plugin-import": "^2.22.1", "husky": "^4.3.0", "jest": "^26.6.1", "lint-staged": "^10.5.0", "lodash": "^4.17.20", "memfs": "^3.2.0", "mini-css-extract-plugin": "^1.6.0", "nodemon": "^2.0.6", "npm-run-all": "^4.1.5", "postcss": "^8.2.14", "postcss-font-magician": "^3.0.0", "postcss-loader": "^5.2.0", "prettier": "^2.1.2", "sass": "^1.27.0", "sass-loader": "^11.0.1", "standard-version": "^9.0.0", "webpack": "^5.3.0"}, "keywords": ["webpack"]}