const http = require('http');
const httpProxy = require('http-proxy-middleware');
const express = require('express');

const app = express();

// 创建 MQTT WebSocket 代理
const mqttProxy = httpProxy.createProxyMiddleware('/mqtt', {
  target: 'ws://121.201.109.17:38084',
  ws: true,
  changeOrigin: true,
  pathRewrite: {
    '^/mqtt': '/mgtt'
  },
  onError: (err, req, res) => {
    console.log('代理错误:', err.message);
  },
  onProxyReqWs: (proxyReq, req, socket, options, head) => {
    console.log('WebSocket 代理请求:', req.url);
  }
});

// 使用代理中间件
app.use('/mqtt', mqttProxy);

// 启用 CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
  next();
});

// 创建 HTTP 服务器
const server = http.createServer(app);

// 升级 WebSocket 连接
server.on('upgrade', mqttProxy.upgrade);

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`MQTT 代理服务器运行在 http://localhost:${PORT}`);
  console.log(`WebSocket 代理地址: ws://localhost:${PORT}/mqtt`);
});
