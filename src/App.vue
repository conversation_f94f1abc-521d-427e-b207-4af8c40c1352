<template>
  <div id="app">
    <div class="container">
      <h1>MQTT 连接测试工具</h1>
      <MqttTestTool />
    </div>
  </div>
</template>

<script>
import MqttTestTool from './components/MqttTestTool.vue';

export default {
  name: 'App',
  components: {
    MqttTestTool
  }
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #42b983;
  margin-bottom: 30px;
}
</style>
