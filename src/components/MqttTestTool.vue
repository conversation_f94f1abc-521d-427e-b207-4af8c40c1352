<template>
  <div class="mqtt-test-tool">
    <!-- 连接配置 -->
    <div class="config-section">
      <h2>MQTT 连接配置</h2>
      <div class="config-form">
        <div class="form-row">
          <label>连接方式:</label>
          <select v-model="connectionType" @change="updateBrokerUrl">
            <option value="proxy">通过代理 (推荐)</option>
            <option value="direct">直连</option>
          </select>
        </div>
        <div class="form-row">
          <label>服务器:</label>
          <input v-model="brokerUrl" class="input-field" readonly />
        </div>
        <div class="form-row">
          <label>用户名:</label>
          <input v-model="username" class="input-field" />
        </div>
        <div class="form-row">
          <label>密码:</label>
          <input v-model="password" type="password" class="input-field" />
        </div>
      </div>
    </div>

    <!-- 连接状态 -->
    <div class="status-section">
      <h2>连接状态</h2>
      <div class="status-display">
        <span :class="['status-dot', connectionStatus]"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
      <div class="action-buttons">
        <button 
          @click="connect" 
          :disabled="connectionStatus === 'connecting'"
          class="btn btn-primary"
        >
          {{ connectionStatus === 'connecting' ? '连接中...' : '连接' }}
        </button>
        <button 
          @click="disconnect" 
          :disabled="!isConnected"
          class="btn btn-secondary"
        >
          断开连接
        </button>
      </div>
    </div>

    <!-- 测试区域 -->
    <div class="test-section" v-if="isConnected">
      <h2>MQTT 测试</h2>
      
      <div class="test-group">
        <h3>订阅测试</h3>
        <div class="test-controls">
          <input 
            v-model="testTopic"
            placeholder="测试主题，例如: test/topic"
            class="input-field"
          />
          <button @click="subscribeTest" class="btn btn-primary">订阅</button>
        </div>
      </div>
      
      <div class="test-group">
        <h3>发布测试</h3>
        <div class="test-controls">
          <input 
            v-model="testMessage"
            placeholder="测试消息"
            class="input-field"
          />
          <button @click="publishTest" class="btn btn-primary">发布</button>
        </div>
      </div>
    </div>

    <!-- 日志 -->
    <div class="log-section">
      <h2>连接日志</h2>
      <div class="log-controls">
        <button @click="clearLogs" class="btn btn-secondary">清空日志</button>
      </div>
      <div class="log-container" ref="logContainer">
        <div 
          v-for="(log, index) in logs" 
          :key="index"
          :class="['log-item', log.type]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="no-logs">
          暂无日志
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mqtt from 'mqtt'

export default {
  name: 'MqttTestTool',
  data() {
    return {
      client: null,
      isConnected: false,
      connectionStatus: 'disconnected',
      connectionType: 'proxy',
      
      // 测试数据
      testTopic: 'test/topic',
      testMessage: 'Hello MQTT!',
      logs: [],
      
      // MQTT 连接配置
      brokerUrl: 'ws://localhost:8080/mqtt', // 默认使用代理
      username: 'admin',
      password: 'sps@123456',
      clientId: 'vue_mqtt_' + Math.random().toString(16).substring(2, 8)
    }
  },
  
  computed: {
    statusText() {
      const statusMap = {
        'disconnected': '未连接',
        'connecting': '连接中...',
        'connected': '已连接',
        'error': '连接错误'
      }
      return statusMap[this.connectionStatus] || '未知状态'
    }
  },
  
  methods: {
    updateBrokerUrl() {
      if (this.connectionType === 'proxy') {
        this.brokerUrl = 'ws://localhost:8080/mqtt'
        this.addLog('info', '切换到代理模式')
      } else {
        this.brokerUrl = 'ws://**************:38084/mgtt'
        this.addLog('info', '切换到直连模式')
      }
    },
    
    connect() {
      this.connectionStatus = 'connecting'
      this.addLog('info', `开始连接: ${this.brokerUrl}`)
      
      const options = {
        username: this.username,
        password: this.password,
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30000,
        reconnectPeriod: 0
      }
      
      try {
        this.client = mqtt.connect(this.brokerUrl, options)
        
        this.client.on('connect', () => {
          this.isConnected = true
          this.connectionStatus = 'connected'
          this.addLog('success', '✅ MQTT 连接成功!')
        })
        
        this.client.on('error', (error) => {
          this.connectionStatus = 'error'
          this.addLog('error', `❌ 连接失败: ${error.message}`)
          setTimeout(() => {
            if (this.connectionStatus === 'error') {
              this.connectionStatus = 'disconnected'
            }
          }, 3000)
        })
        
        this.client.on('message', (topic, message) => {
          const payload = message.toString()
          this.addLog('message', `📨 收到消息 [${topic}]: ${payload}`)
        })
        
        this.client.on('close', () => {
          if (this.isConnected) {
            this.isConnected = false
            this.connectionStatus = 'disconnected'
            this.addLog('info', '🔌 连接已断开')
          }
        })
        
      } catch (error) {
        this.connectionStatus = 'error'
        this.addLog('error', `创建客户端失败: ${error.message}`)
      }
    },
    
    disconnect() {
      if (this.client) {
        this.client.end()
        this.client = null
        this.isConnected = false
        this.connectionStatus = 'disconnected'
        this.addLog('info', '主动断开连接')
      }
    },
    
    subscribeTest() {
      if (!this.isConnected || !this.testTopic.trim()) return
      
      this.client.subscribe(this.testTopic, (error) => {
        if (error) {
          this.addLog('error', `订阅失败: ${error.message}`)
        } else {
          this.addLog('success', `✅ 订阅成功: ${this.testTopic}`)
        }
      })
    },
    
    publishTest() {
      if (!this.isConnected || !this.testTopic.trim() || !this.testMessage.trim()) return
      
      this.client.publish(this.testTopic, this.testMessage, (error) => {
        if (error) {
          this.addLog('error', `发布失败: ${error.message}`)
        } else {
          this.addLog('success', `✅ 发布成功 [${this.testTopic}]: ${this.testMessage}`)
        }
      })
    },
    
    addLog(type, message) {
      const log = {
        type,
        message,
        time: new Date().toLocaleTimeString()
      }
      this.logs.push(log)
      
      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(-50)
      }
      
      // 自动滚动到底部
      this.$nextTick(() => {
        const container = this.$refs.logContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },
    
    clearLogs() {
      this.logs = []
    }
  },
  
  beforeDestroy() {
    this.disconnect()
  }
}
</script>

<style scoped>
.mqtt-test-tool {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.config-section,
.status-section,
.test-section,
.log-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

h3 {
  color: #555;
  margin-bottom: 10px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.form-row label {
  width: 80px;
  font-weight: bold;
  color: #333;
}

.input-field,
select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.status-display {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.connected {
  background-color: #4caf50;
}

.status-dot.connecting {
  background-color: #ff9800;
  animation: pulse 1s infinite;
}

.status-dot.disconnected {
  background-color: #9e9e9e;
}

.status-dot.error {
  background-color: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.test-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.test-controls .input-field {
  flex: 1;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 5px;
  padding: 5px;
  border-radius: 3px;
  font-size: 13px;
}

.log-item.success {
  background-color: #d4edda;
  color: #155724;
}

.log-item.error {
  background-color: #f8d7da;
  color: #721c24;
}

.log-item.info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.log-item.message {
  background-color: #fff3cd;
  color: #856404;
}

.log-time {
  font-weight: bold;
  margin-right: 10px;
}

.no-logs {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}
</style>
