<template>
  <div class="mqtt-client">
    <!-- 连接状态 -->
    <div class="status-section">
      <h2>连接状态</h2>
      <div class="status-indicator">
        <span :class="['status-dot', connectionStatus]"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
      <div v-if="connectionStatus === 'connecting'" class="connection-info">
        <small>正在连接: {{ brokerUrl }}</small>
      </div>
      <div v-if="connectionStatus === 'connected'" class="connection-info">
        <small>已连接到: {{ brokerUrl }}</small>
      </div>
      <div class="connection-actions">
        <button 
          @click="connect" 
          :disabled="isConnected"
          class="btn btn-primary"
        >
          连接
        </button>
        <button
          @click="disconnect"
          :disabled="!isConnected"
          class="btn btn-secondary"
        >
          断开连接
        </button>
        <button
          @click="tryDifferentAuth"
          :disabled="connectionStatus === 'connecting'"
          class="btn btn-secondary"
        >
          尝试无认证连接
        </button>
        <button
          @click="tryDirectConnection"
          :disabled="connectionStatus === 'connecting'"
          class="btn btn-secondary"
        >
          尝试直连
        </button>
        <button
          @click="tryProxyConnection"
          class="btn btn-secondary"
        >
          通过代理连接
        </button>
        <button
          @click="forceReset"
          class="btn btn-secondary"
        >
          重置状态
        </button>
      </div>
    </div>

    <!-- 订阅主题 -->
    <div class="subscribe-section">
      <h2>订阅主题</h2>
      <div class="input-group">
        <input 
          v-model="subscribeTopicInput"
          placeholder="输入要订阅的主题，例如: test/topic"
          class="input-field"
          @keyup.enter="subscribeTopic"
        />
        <button 
          @click="subscribeTopic"
          :disabled="!isConnected || !subscribeTopicInput.trim()"
          class="btn btn-primary"
        >
          订阅
        </button>
      </div>
      
      <div v-if="subscribedTopics.length > 0" class="subscribed-topics">
        <h3>已订阅的主题:</h3>
        <div class="topic-list">
          <div 
            v-for="topic in subscribedTopics" 
            :key="topic"
            class="topic-item"
          >
            <span>{{ topic }}</span>
            <button 
              @click="unsubscribeTopic(topic)"
              class="btn btn-small btn-danger"
            >
              取消订阅
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 发布消息 -->
    <div class="publish-section">
      <h2>发布消息</h2>
      <div class="publish-form">
        <div class="input-group">
          <label>主题:</label>
          <input 
            v-model="publishTopic"
            placeholder="输入发布主题"
            class="input-field"
          />
        </div>
        <div class="input-group">
          <label>消息:</label>
          <textarea 
            v-model="publishMessage"
            placeholder="输入要发布的消息"
            class="textarea-field"
            rows="3"
          ></textarea>
        </div>
        <button 
          @click="publishMsg"
          :disabled="!isConnected || !publishTopic.trim() || !publishMessage.trim()"
          class="btn btn-primary"
        >
          发布消息
        </button>
      </div>
    </div>

    <!-- 消息日志 -->
    <div class="messages-section">
      <h2>消息日志</h2>
      <div class="message-controls">
        <button @click="clearMessages" class="btn btn-secondary">清空日志</button>
        <label class="auto-scroll-label">
          <input type="checkbox" v-model="autoScroll" />
          自动滚动
        </label>
      </div>
      <div class="messages-container" ref="messagesContainer">
        <div 
          v-for="(message, index) in messages" 
          :key="index"
          :class="['message-item', message.type]"
        >
          <div class="message-header">
            <span class="message-time">{{ message.timestamp }}</span>
            <span class="message-type">{{ message.type.toUpperCase() }}</span>
          </div>
          <div class="message-content">
            <strong>主题:</strong> {{ message.topic }}<br>
            <strong>消息:</strong> {{ message.payload }}
          </div>
        </div>
        <div v-if="messages.length === 0" class="no-messages">
          暂无消息
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mqtt from 'mqtt'

export default {
  name: 'MqttClient',
  data() {
    return {
      client: null,
      isConnected: false,
      connectionStatus: 'disconnected',
      subscribeTopicInput: '',
      subscribedTopics: [],
      publishTopic: '',
      publishMessage: '',
      messages: [],
      autoScroll: true,

      // MQTT 连接配置
      brokerUrl: 'ws://**************:38084/mgtt', // 直连地址
      username: 'admin',
      password: 'sps@123456',
      clientId: 'vue_mqtt_client_' + Math.random().toString(16).substring(2, 10)
    };
  },
  computed: {
    statusText() {
      switch (this.connectionStatus) {
        case 'connected':
          return '已连接';
        case 'connecting':
          return '连接中...';
        case 'disconnected':
          return '未连接';
        case 'error':
          return '连接错误';
        default:
          return '未知状态';
      }
    }
  },
  methods: {
    connect() {
      // 先重置状态
      this.forceReset()
      this.connectionStatus = 'connecting'
      this.addMessage('system', '=== 开始 MQTT 连接流程 ===', '连接日志')
      this.addMessage('system', `步骤 1: 准备连接参数`, '连接日志')
      this.addMessage('system', `- 服务器地址: ${this.brokerUrl}`, '连接日志')
      this.addMessage('system', `- 用户名: ${this.username}`, '连接日志')
      this.addMessage('system', `- 密码: ${this.password ? '***已设置***' : '未设置'}`, '连接日志')
      this.addMessage('system', `- 客户端ID: ${this.clientId}`, '连接日志')

      // MQTT 连接选项
      const options = {
        username: this.username,
        password: this.password,
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0, // 禁用自动重连
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        // WebSocket 特定选项
        wsOptions: {
          perMessageDeflate: false
        }
      }

      this.addMessage('system', `步骤 2: 连接选项配置完成`, '连接日志')
      this.addMessage('system', `- 协议版本: MQTT ${options.protocolVersion}`, '连接日志')
      this.addMessage('system', `- 保持连接: ${options.keepalive}秒`, '连接日志')
      this.addMessage('system', `- 连接超时: ${options.connectTimeout/1000}秒`, '连接日志')
      this.addMessage('system', `- 清理会话: ${options.clean}`, '连接日志')

      this.addMessage('system', `步骤 3: 创建 MQTT 客户端...`, '连接日志')

      try {
        // 创建 MQTT 客户端
        this.client = mqtt.connect(this.brokerUrl, options)
        this.addMessage('system', `步骤 4: MQTT 客户端创建成功`, '连接日志')
        this.addMessage('system', `步骤 5: 开始建立连接...`, '连接日志')
      } catch (error) {
        this.addMessage('error', `步骤 4 失败: 创建客户端时出错 - ${error.message}`, '连接日志')
        this.connectionStatus = 'error'
        return
      }

      // 连接成功
      this.client.on('connect', (connack) => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', `✅ 步骤 6: MQTT连接成功!`, '连接日志')
        this.addMessage('system', `- 会话存在: ${connack.sessionPresent}`, '连接日志')
        this.addMessage('system', `- 返回代码: ${connack.returnCode}`, '连接日志')
        this.addMessage('system', `=== 连接流程完成 ===`, '连接日志')
        console.log('MQTT连接成功, connack:', connack)
      })

      // 连接错误
      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        this.addMessage('error', `❌ 连接错误发生`, '连接日志')
        this.addMessage('error', `- 错误类型: ${error.name || 'Unknown'}`, '连接日志')
        this.addMessage('error', `- 错误消息: ${error.message}`, '连接日志')
        if (error.code) this.addMessage('error', `- 错误代码: ${error.code}`, '连接日志')
        if (error.errno) this.addMessage('error', `- 系统错误号: ${error.errno}`, '连接日志')
        if (error.syscall) this.addMessage('error', `- 系统调用: ${error.syscall}`, '连接日志')
        if (error.stack) console.error('完整错误堆栈:', error.stack)
        console.error('MQTT连接错误:', error)
      })

      // 接收消息
      this.client.on('message', (topic, message, packet) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
        this.addMessage('system', `📨 收到消息 - 主题: ${topic}, 长度: ${payload.length}`, '消息日志')
        console.log('收到消息:', { topic, payload, packet })
      })

      // 连接断开
      this.client.on('close', () => {
        this.addMessage('system', `🔌 连接已关闭`, '连接日志')
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT连接已断开', '连接日志')
        }
      })

      // 离线状态
      this.client.on('offline', () => {
        this.addMessage('error', `📴 客户端离线 - 可能是认证失败或网络问题`, '连接日志')
      })

      // 重连尝试
      this.client.on('reconnect', () => {
        this.addMessage('system', `🔄 尝试重连...`, '连接日志')
      })

      // 连接结束
      this.client.on('end', () => {
        this.addMessage('system', `🏁 连接已结束`, '连接日志')
      })

      // 数据包发送（调试用）
      this.client.on('packetsend', (packet) => {
        this.addMessage('system', `📤 发送数据包: ${packet.cmd}`, '数据包日志')
        if (packet.cmd === 'connect') {
          this.addMessage('system', `- CONNECT 包详情:`, '数据包日志')
          this.addMessage('system', `  * 协议名: ${packet.protocolId}`, '数据包日志')
          this.addMessage('system', `  * 协议版本: ${packet.protocolVersion}`, '数据包日志')
          this.addMessage('system', `  * 客户端ID: ${packet.clientId}`, '数据包日志')
          this.addMessage('system', `  * 用户名: ${packet.username || '无'}`, '数据包日志')
          this.addMessage('system', `  * 密码: ${packet.password ? '已设置' : '无'}`, '数据包日志')
        }
        console.log('发送数据包:', packet)
      })

      // 数据包接收（调试用）
      this.client.on('packetreceive', (packet) => {
        this.addMessage('system', `📥 接收数据包: ${packet.cmd}`, '数据包日志')

        if (packet.cmd === 'connack') {
          this.addMessage('system', `- CONNACK 包详情:`, '数据包日志')
          this.addMessage('system', `  * 返回代码: ${packet.returnCode}`, '数据包日志')
          this.addMessage('system', `  * 会话存在: ${packet.sessionPresent}`, '数据包日志')

          if (packet.returnCode !== 0) {
            let errorMsg = '认证失败'
            switch (packet.returnCode) {
              case 1: errorMsg = '协议版本不支持'; break
              case 2: errorMsg = '客户端ID被拒绝'; break
              case 3: errorMsg = '服务器不可用'; break
              case 4: errorMsg = '用户名或密码错误'; break
              case 5: errorMsg = '未授权'; break
              default: errorMsg = `连接被拒绝 (代码: ${packet.returnCode})`
            }
            this.addMessage('error', `❌ ${errorMsg}`, '认证错误')
          } else {
            this.addMessage('system', `✅ 服务器接受连接`, '数据包日志')
          }
        }

        console.log('接收数据包:', packet)
      })

      // WebSocket 特定事件（如果可用）
      if (this.client.stream && this.client.stream.on) {
        this.client.stream.on('open', () => {
          this.addMessage('system', `🌐 WebSocket 连接已建立`, '连接日志')
        })

        this.client.stream.on('error', (error) => {
          this.addMessage('error', `🌐 WebSocket 错误: ${error.message}`, '连接日志')
        })

        this.client.stream.on('close', (code, reason) => {
          this.addMessage('system', `🌐 WebSocket 关闭: 代码=${code}, 原因=${reason}`, '连接日志')
        })
      }
    },

    // 尝试不同的认证方式
    tryDifferentAuth() {
      // 先重置状态
      this.forceReset()
      this.connectionStatus = 'connecting'
      this.addMessage('system', '=== 开始无认证连接测试 ===', '连接日志')
      this.addMessage('system', `步骤 1: 准备无认证连接参数`, '连接日志')
      this.addMessage('system', `- 服务器地址: ${this.brokerUrl}`, '连接日志')
      this.addMessage('system', `- 认证方式: 无用户名密码`, '连接日志')
      this.addMessage('system', `- 客户端ID: ${this.clientId}`, '连接日志')

      const options = {
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0,
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        wsOptions: {
          perMessageDeflate: false
        }
      }

      this.addMessage('system', `步骤 2: 创建无认证客户端...`, '连接日志')

      try {
        this.client = mqtt.connect(this.brokerUrl, options)
        this.addMessage('system', `步骤 3: 开始无认证连接...`, '连接日志')
      } catch (error) {
        this.addMessage('error', `无认证连接创建失败: ${error.message}`, '连接日志')
        this.connectionStatus = 'error'
        return
      }

      this.client.on('connect', (connack) => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', '✅ 无认证连接成功!', '连接日志')
        this.addMessage('system', `- 这说明服务器允许匿名连接`, '连接日志')
        this.addMessage('system', `=== 无认证连接测试完成 ===`, '连接日志')
      })

      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        this.addMessage('error', `❌ 无认证连接失败`, '连接日志')
        this.addMessage('error', `- 错误: ${error.message}`, '连接日志')
        this.addMessage('error', `- 这说明服务器需要认证`, '连接日志')
      })

      this.client.on('message', (topic, message) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
      })

      this.client.on('close', () => {
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT连接已断开', '连接日志')
        }
      })

      this.client.on('offline', () => {
        this.addMessage('error', '📴 无认证连接也离线了', '连接日志')
      })

      // 添加数据包监听
      this.client.on('packetsend', (packet) => {
        this.addMessage('system', `📤 无认证发送: ${packet.cmd}`, '数据包日志')
        console.log('无认证发送数据包:', packet)
      })

      this.client.on('packetreceive', (packet) => {
        this.addMessage('system', `📥 无认证接收: ${packet.cmd}`, '数据包日志')
        if (packet.cmd === 'connack' && packet.returnCode !== 0) {
          this.addMessage('error', `❌ 无认证被拒绝: 返回代码 ${packet.returnCode}`, '连接日志')
        }
        console.log('无认证接收数据包:', packet)
      })
    },

    // 尝试直连（不通过代理）
    tryDirectConnection() {
      // 先重置状态
      this.forceReset()
      this.connectionStatus = 'connecting'
      const directUrl = 'ws://**************:38084/mgtt'
      this.addMessage('system', '=== 开始直连测试 ===', '连接日志')
      this.addMessage('system', `步骤 1: 准备直连参数`, '连接日志')
      this.addMessage('system', `- 直连地址: ${directUrl}`, '连接日志')
      this.addMessage('system', `- 用户名: ${this.username}`, '连接日志')
      this.addMessage('system', `- 密码: ${this.password ? '***已设置***' : '未设置'}`, '连接日志')
      this.addMessage('system', `- 客户端ID: ${this.clientId}`, '连接日志')
      this.addMessage('system', `- 注意: 直连可能受到浏览器跨域策略限制`, '连接日志')

      const options = {
        username: this.username,
        password: this.password,
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0,
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        wsOptions: {
          perMessageDeflate: false
        }
      }

      this.addMessage('system', `步骤 2: 创建直连客户端...`, '连接日志')

      try {
        this.client = mqtt.connect(directUrl, options)
        this.addMessage('system', `步骤 3: 开始直连...`, '连接日志')
      } catch (error) {
        this.addMessage('error', `直连创建失败: ${error.message}`, '连接日志')
        this.connectionStatus = 'error'
        return
      }

      this.client.on('connect', (connack) => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', '✅ 直连成功!', '连接日志')
        this.addMessage('system', `- 这说明没有跨域问题`, '连接日志')
        this.addMessage('system', `=== 直连测试完成 ===`, '连接日志')
        console.log('直连成功, connack:', connack)
      })

      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        this.addMessage('error', `❌ 直连失败`, '连接日志')
        this.addMessage('error', `- 错误: ${error.message}`, '连接日志')
        if (error.code === 'ECONNREFUSED') {
          this.addMessage('error', `- 可能原因: 服务器拒绝连接`, '连接日志')
        } else if (error.message.includes('CORS')) {
          this.addMessage('error', `- 可能原因: 跨域策略限制`, '连接日志')
        }
        console.error('直连错误:', error)
      })

      this.client.on('message', (topic, message) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
      })

      this.client.on('close', () => {
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT连接已断开', '连接日志')
        }
      })

      this.client.on('offline', () => {
        this.addMessage('error', '📴 直连离线', '连接日志')
      })

      // 添加数据包监听
      this.client.on('packetsend', (packet) => {
        this.addMessage('system', `📤 直连发送: ${packet.cmd}`, '数据包日志')
        console.log('直连发送数据包:', packet)
      })

      this.client.on('packetreceive', (packet) => {
        this.addMessage('system', `📥 直连接收: ${packet.cmd}`, '数据包日志')
        console.log('直连接收数据包:', packet)
      })
    },

    // 通过代理连接
    tryProxyConnection() {
      // 先重置状态
      this.forceReset()
      this.connectionStatus = 'connecting'
      const proxyUrl = 'ws://localhost:3001/mqtt'
      this.addMessage('system', '=== 开始代理连接测试 ===', '连接日志')
      this.addMessage('system', `步骤 1: 准备代理连接参数`, '连接日志')
      this.addMessage('system', `- 代理地址: ${proxyUrl}`, '连接日志')
      this.addMessage('system', `- 最终目标: ws://**************:38084/mgtt`, '连接日志')
      this.addMessage('system', `- 用户名: ${this.username}`, '连接日志')
      this.addMessage('system', `- 密码: ${this.password ? '***已设置***' : '未设置'}`, '连接日志')
      this.addMessage('system', `- 客户端ID: ${this.clientId}`, '连接日志')
      this.addMessage('system', `- 注意: 需要先启动代理服务器 (npm run proxy)`, '连接日志')

      const options = {
        username: this.username,
        password: this.password,
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0,
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        wsOptions: {
          perMessageDeflate: false
        }
      }

      this.addMessage('system', `步骤 2: 创建代理客户端...`, '连接日志')

      try {
        this.client = mqtt.connect(proxyUrl, options)
        this.addMessage('system', `步骤 3: 开始代理连接...`, '连接日志')
      } catch (error) {
        this.addMessage('error', `代理连接创建失败: ${error.message}`, '连接日志')
        this.connectionStatus = 'error'
        return
      }

      this.client.on('connect', (connack) => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', '✅ 代理连接成功!', '连接日志')
        this.addMessage('system', `- 代理服务器工作正常`, '连接日志')
        this.addMessage('system', `- 成功绕过跨域限制`, '连接日志')
        this.addMessage('system', `=== 代理连接测试完成 ===`, '连接日志')
        console.log('代理连接成功, connack:', connack)
      })

      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        this.addMessage('error', `❌ 代理连接失败`, '连接日志')
        this.addMessage('error', `- 错误: ${error.message}`, '连接日志')
        if (error.code === 'ECONNREFUSED') {
          this.addMessage('error', `- 可能原因: 代理服务器未启动`, '连接日志')
          this.addMessage('error', `- 解决方案: 运行 'npm run proxy' 启动代理`, '连接日志')
        }
        console.error('代理连接错误:', error)
      })

      this.client.on('message', (topic, message) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
        this.addMessage('system', `📨 通过代理收到消息`, '连接日志')
      })

      this.client.on('close', () => {
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT代理连接已断开', '连接日志')
        }
      })

      this.client.on('offline', () => {
        this.addMessage('error', '📴 代理连接离线', '连接日志')
      })

      // 添加数据包监听
      this.client.on('packetsend', (packet) => {
        this.addMessage('system', `📤 代理发送: ${packet.cmd}`, '数据包日志')
        console.log('代理发送数据包:', packet)
      })

      this.client.on('packetreceive', (packet) => {
        this.addMessage('system', `📥 代理接收: ${packet.cmd}`, '数据包日志')
        console.log('代理接收数据包:', packet)
      })
    },

    // 强制重置连接状态
    forceReset() {
      this.addMessage('system', '🔄 强制重置连接状态', '系统消息')

      // 强制断开现有连接
      if (this.client) {
        try {
          this.client.end(true) // 强制关闭
        } catch (error) {
          console.log('强制关闭客户端时出错:', error)
        }
        this.client = null
      }

      // 重置状态
      this.isConnected = false
      this.connectionStatus = 'disconnected'
      this.subscribedTopics = []

      this.addMessage('system', '✅ 状态已重置，可以重新连接', '系统消息')
    },

    disconnect() {
      if (this.client) {
        this.client.end()
        this.client = null
        this.isConnected = false
        this.connectionStatus = 'disconnected'
        this.subscribedTopics = []
        this.addMessage('system', '已断开连接', '系统消息')
      }
    },

    subscribeTopic() {
      if (!this.isConnected || !this.subscribeTopicInput.trim()) return

      const topic = this.subscribeTopicInput.trim()

      this.client.subscribe(topic, (error) => {
        if (error) {
          this.addMessage('error', error.message, `订阅失败: ${topic}`)
          console.error('订阅失败:', error)
        } else {
          this.subscribedTopics.push(topic)
          this.addMessage('system', `成功订阅主题: ${topic}`, '系统消息')
          this.subscribeTopicInput = ''
          console.log('订阅成功:', topic)
        }
      })
    },

    unsubscribeTopic(topic) {
      this.client.unsubscribe(topic, (error) => {
        if (error) {
          this.addMessage('error', error.message, `取消订阅失败: ${topic}`)
          console.error('取消订阅失败:', error)
        } else {
          this.subscribedTopics = this.subscribedTopics.filter(t => t !== topic)
          this.addMessage('system', `已取消订阅主题: ${topic}`, '系统消息')
          console.log('取消订阅成功:', topic)
        }
      })
    },

    publishMsg() {
      if (!this.isConnected || !this.publishTopic.trim() || !this.publishMessage.trim()) return

      this.client.publish(this.publishTopic, this.publishMessage, (error) => {
        if (error) {
          this.addMessage('error', error.message, `发布失败: ${this.publishTopic}`)
          console.error('发布失败:', error)
        } else {
          this.addMessage('sent', this.publishMessage, this.publishTopic)
          console.log('消息发布成功:', this.publishTopic, this.publishMessage)
          this.publishMessage = ''
        }
      })
    },

    addMessage(type, payload, topic) {
      const message = {
        type,
        topic,
        payload,
        timestamp: new Date().toLocaleTimeString()
      };
      
      this.messages.push(message);
      
      // 限制消息数量，避免内存溢出
      if (this.messages.length > 1000) {
        this.messages = this.messages.slice(-500);
      }
      
      // 自动滚动到底部
      if (this.autoScroll) {
        this.$nextTick(() => {
          const container = this.$refs.messagesContainer;
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
        });
      }
    },

    clearMessages() {
      this.messages = [];
    }
  },

  beforeDestroy() {
    this.disconnect();
  }
};
</script>

<style scoped>
.mqtt-client {
  max-width: 1000px;
  margin: 0 auto;
}

.status-section,
.subscribe-section,
.publish-section,
.messages-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.connected {
  background-color: #4caf50;
}

.status-dot.connecting {
  background-color: #ff9800;
  animation: pulse 1s infinite;
}

.status-dot.disconnected {
  background-color: #9e9e9e;
}

.status-dot.error {
  background-color: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.input-field,
.textarea-field {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.input-group {
  margin-bottom: 15px;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
}

.messages-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.message-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.message-item.received {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
}

.message-item.sent {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
}

.message-item.system {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}

.message-item.error {
  background-color: #ffebee;
  border-left-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.message-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}
</style>

<style scoped>
.mqtt-client {
  max-width: 1000px;
  margin: 0 auto;
}

.status-section,
.subscribe-section,
.publish-section,
.messages-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

h3 {
  color: #555;
  margin-bottom: 10px;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.connected {
  background-color: #4caf50;
}

.status-dot.connecting {
  background-color: #ff9800;
  animation: pulse 1s infinite;
}

.status-dot.disconnected {
  background-color: #9e9e9e;
}

.status-dot.error {
  background-color: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: bold;
  color: #333;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.input-field,
.textarea-field {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.input-field:focus,
.textarea-field:focus {
  outline: none;
  border-color: #42b983;
  box-shadow: 0 0 0 2px rgba(66, 185, 131, 0.2);
}

.textarea-field {
  resize: vertical;
  min-height: 60px;
}

/* 主题列表 */
.topic-list {
  max-height: 200px;
  overflow-y: auto;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
}

/* 消息控制 */
.message-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.auto-scroll-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.auto-scroll-label input {
  margin-right: 5px;
}

/* 消息容器 */
.messages-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.message-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.message-item.received {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
}

.message-item.sent {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
}

.message-item.system {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}

.message-item.error {
  background-color: #ffebee;
  border-left-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.message-type {
  font-weight: bold;
}

.message-content {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

/* 连接操作按钮 */
.connection-actions {
  display: flex;
  gap: 10px;
}

/* 发布表单 */
.publish-form {
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mqtt-client {
    padding: 10px;
  }

  .status-section,
  .subscribe-section,
  .publish-section,
  .messages-section {
    padding: 15px;
  }

  .connection-actions {
    flex-direction: column;
  }

  .btn {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .message-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
