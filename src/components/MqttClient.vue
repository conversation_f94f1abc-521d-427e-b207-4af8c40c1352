<template>
  <div class="mqtt-client">
    <!-- 连接状态 -->
    <div class="status-section">
      <h2>连接状态</h2>
      <div class="status-indicator">
        <span :class="['status-dot', connectionStatus]"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
      <div v-if="connectionStatus === 'connecting'" class="connection-info">
        <small>正在连接: {{ brokerUrl }}</small>
      </div>
      <div v-if="connectionStatus === 'connected'" class="connection-info">
        <small>已连接到: {{ brokerUrl }}</small>
      </div>
      <div class="connection-actions">
        <button 
          @click="connect" 
          :disabled="isConnected"
          class="btn btn-primary"
        >
          连接
        </button>
        <button
          @click="disconnect"
          :disabled="!isConnected"
          class="btn btn-secondary"
        >
          断开连接
        </button>
        <button
          @click="tryDifferentAuth"
          :disabled="connectionStatus === 'connecting'"
          class="btn btn-secondary"
        >
          尝试无认证连接
        </button>
        <button
          @click="tryDirectConnection"
          :disabled="connectionStatus === 'connecting'"
          class="btn btn-secondary"
        >
          尝试直连
        </button>
      </div>
    </div>

    <!-- 订阅主题 -->
    <div class="subscribe-section">
      <h2>订阅主题</h2>
      <div class="input-group">
        <input 
          v-model="subscribeTopicInput"
          placeholder="输入要订阅的主题，例如: test/topic"
          class="input-field"
          @keyup.enter="subscribeTopic"
        />
        <button 
          @click="subscribeTopic"
          :disabled="!isConnected || !subscribeTopicInput.trim()"
          class="btn btn-primary"
        >
          订阅
        </button>
      </div>
      
      <div v-if="subscribedTopics.length > 0" class="subscribed-topics">
        <h3>已订阅的主题:</h3>
        <div class="topic-list">
          <div 
            v-for="topic in subscribedTopics" 
            :key="topic"
            class="topic-item"
          >
            <span>{{ topic }}</span>
            <button 
              @click="unsubscribeTopic(topic)"
              class="btn btn-small btn-danger"
            >
              取消订阅
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 发布消息 -->
    <div class="publish-section">
      <h2>发布消息</h2>
      <div class="publish-form">
        <div class="input-group">
          <label>主题:</label>
          <input 
            v-model="publishTopic"
            placeholder="输入发布主题"
            class="input-field"
          />
        </div>
        <div class="input-group">
          <label>消息:</label>
          <textarea 
            v-model="publishMessage"
            placeholder="输入要发布的消息"
            class="textarea-field"
            rows="3"
          ></textarea>
        </div>
        <button 
          @click="publishMsg"
          :disabled="!isConnected || !publishTopic.trim() || !publishMessage.trim()"
          class="btn btn-primary"
        >
          发布消息
        </button>
      </div>
    </div>

    <!-- 消息日志 -->
    <div class="messages-section">
      <h2>消息日志</h2>
      <div class="message-controls">
        <button @click="clearMessages" class="btn btn-secondary">清空日志</button>
        <label class="auto-scroll-label">
          <input type="checkbox" v-model="autoScroll" />
          自动滚动
        </label>
      </div>
      <div class="messages-container" ref="messagesContainer">
        <div 
          v-for="(message, index) in messages" 
          :key="index"
          :class="['message-item', message.type]"
        >
          <div class="message-header">
            <span class="message-time">{{ message.timestamp }}</span>
            <span class="message-type">{{ message.type.toUpperCase() }}</span>
          </div>
          <div class="message-content">
            <strong>主题:</strong> {{ message.topic }}<br>
            <strong>消息:</strong> {{ message.payload }}
          </div>
        </div>
        <div v-if="messages.length === 0" class="no-messages">
          暂无消息
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mqtt from 'mqtt'

export default {
  name: 'MqttClient',
  data() {
    return {
      client: null,
      isConnected: false,
      connectionStatus: 'disconnected',
      subscribeTopicInput: '',
      subscribedTopics: [],
      publishTopic: '',
      publishMessage: '',
      messages: [],
      autoScroll: true,

      // MQTT 连接配置
      brokerUrl: 'ws://localhost:8080/mqtt-proxy', // 使用代理路径
      directUrl: 'ws://**************:38084/mgtt', // 直连地址（备用）
      username: 'admin',
      password: 'sps@123456',
      clientId: 'vue_mqtt_client_' + Math.random().toString(16).substring(2, 10)
    };
  },
  computed: {
    statusText() {
      switch (this.connectionStatus) {
        case 'connected':
          return '已连接';
        case 'connecting':
          return '连接中...';
        case 'disconnected':
          return '未连接';
        case 'error':
          return '连接错误';
        default:
          return '未知状态';
      }
    }
  },
  methods: {
    connect() {
      this.connectionStatus = 'connecting'
      this.addMessage('system', `尝试连接: ${this.brokerUrl}`, '系统消息')
      this.addMessage('system', `使用认证: ${this.username}`, '系统消息')

      // MQTT 连接选项
      const options = {
        username: this.username,
        password: this.password,
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0, // 禁用自动重连
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        // WebSocket 特定选项
        wsOptions: {
          perMessageDeflate: false
        }
      }

      // 创建 MQTT 客户端
      this.client = mqtt.connect(this.brokerUrl, options)

      // 连接成功
      this.client.on('connect', () => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', `MQTT连接成功!`, '系统消息')
        console.log('MQTT连接成功')
      })

      // 连接错误
      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        console.error('MQTT连接错误:', error)

        let errorMsg = error.message || '连接失败'
        if (error.code) errorMsg += ` (${error.code})`

        this.addMessage('error', errorMsg, '连接错误')
      })

      // 接收消息
      this.client.on('message', (topic, message) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
        console.log('收到消息:', topic, payload)
      })

      // 连接断开
      this.client.on('close', () => {
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT连接已断开', '系统消息')
        }
      })

      // 离线状态
      this.client.on('offline', () => {
        this.addMessage('error', '客户端离线 - 可能是认证失败', '连接状态')
      })

      // 重连尝试
      this.client.on('reconnect', () => {
        this.addMessage('system', '尝试重连...', '连接状态')
      })

      // 连接结束
      this.client.on('end', () => {
        this.addMessage('system', '连接已结束', '连接状态')
      })

      // 数据包接收（调试用）
      this.client.on('packetsend', (packet) => {
        console.log('发送数据包:', packet)
      })

      this.client.on('packetreceive', (packet) => {
        console.log('接收数据包:', packet)
        if (packet.cmd === 'connack') {
          if (packet.returnCode !== 0) {
            let errorMsg = '认证失败'
            switch (packet.returnCode) {
              case 1: errorMsg = '协议版本不支持'; break
              case 2: errorMsg = '客户端ID被拒绝'; break
              case 3: errorMsg = '服务器不可用'; break
              case 4: errorMsg = '用户名或密码错误'; break
              case 5: errorMsg = '未授权'; break
              default: errorMsg = `连接被拒绝 (代码: ${packet.returnCode})`
            }
            this.addMessage('error', errorMsg, '认证错误')
          }
        }
      })
    },

    // 尝试不同的认证方式
    tryDifferentAuth() {
      this.connectionStatus = 'connecting'
      this.addMessage('system', '尝试无认证连接...', '系统消息')

      const options = {
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0,
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        wsOptions: {
          perMessageDeflate: false
        }
      }

      this.client = mqtt.connect(this.brokerUrl, options)

      this.client.on('connect', () => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', 'MQTT无认证连接成功!', '系统消息')
      })

      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        this.addMessage('error', `无认证连接失败: ${error.message}`, '连接错误')
      })

      this.client.on('message', (topic, message) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
      })

      this.client.on('close', () => {
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT连接已断开', '系统消息')
        }
      })

      this.client.on('offline', () => {
        this.addMessage('error', '无认证连接也离线了', '连接状态')
      })
    },

    // 尝试直连（不通过代理）
    tryDirectConnection() {
      this.connectionStatus = 'connecting'
      this.addMessage('system', `尝试直连: ${this.directUrl}`, '系统消息')
      this.addMessage('system', `使用认证: ${this.username}`, '系统消息')

      const options = {
        username: this.username,
        password: this.password,
        clientId: this.clientId,
        clean: true,
        keepalive: 60,
        connectTimeout: 30 * 1000,
        reconnectPeriod: 0,
        rejectUnauthorized: false,
        protocolId: 'MQTT',
        protocolVersion: 4,
        wsOptions: {
          perMessageDeflate: false
        }
      }

      this.client = mqtt.connect(this.directUrl, options)

      this.client.on('connect', () => {
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.addMessage('system', 'MQTT直连成功!', '系统消息')
      })

      this.client.on('error', (error) => {
        this.connectionStatus = 'error'
        this.addMessage('error', `直连失败: ${error.message}`, '连接错误')
      })

      this.client.on('message', (topic, message) => {
        const payload = message.toString()
        this.addMessage('received', payload, topic)
      })

      this.client.on('close', () => {
        if (this.isConnected) {
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.addMessage('system', 'MQTT连接已断开', '系统消息')
        }
      })

      this.client.on('offline', () => {
        this.addMessage('error', '直连也离线了', '连接状态')
      })
    },

    disconnect() {
      if (this.client) {
        this.client.end()
        this.client = null
        this.isConnected = false
        this.connectionStatus = 'disconnected'
        this.subscribedTopics = []
        this.addMessage('system', '已断开连接', '系统消息')
      }
    },

    subscribeTopic() {
      if (!this.isConnected || !this.subscribeTopicInput.trim()) return

      const topic = this.subscribeTopicInput.trim()

      this.client.subscribe(topic, (error) => {
        if (error) {
          this.addMessage('error', error.message, `订阅失败: ${topic}`)
          console.error('订阅失败:', error)
        } else {
          this.subscribedTopics.push(topic)
          this.addMessage('system', `成功订阅主题: ${topic}`, '系统消息')
          this.subscribeTopicInput = ''
          console.log('订阅成功:', topic)
        }
      })
    },

    unsubscribeTopic(topic) {
      this.client.unsubscribe(topic, (error) => {
        if (error) {
          this.addMessage('error', error.message, `取消订阅失败: ${topic}`)
          console.error('取消订阅失败:', error)
        } else {
          this.subscribedTopics = this.subscribedTopics.filter(t => t !== topic)
          this.addMessage('system', `已取消订阅主题: ${topic}`, '系统消息')
          console.log('取消订阅成功:', topic)
        }
      })
    },

    publishMsg() {
      if (!this.isConnected || !this.publishTopic.trim() || !this.publishMessage.trim()) return

      this.client.publish(this.publishTopic, this.publishMessage, (error) => {
        if (error) {
          this.addMessage('error', error.message, `发布失败: ${this.publishTopic}`)
          console.error('发布失败:', error)
        } else {
          this.addMessage('sent', this.publishMessage, this.publishTopic)
          console.log('消息发布成功:', this.publishTopic, this.publishMessage)
          this.publishMessage = ''
        }
      })
    },

    addMessage(type, payload, topic) {
      const message = {
        type,
        topic,
        payload,
        timestamp: new Date().toLocaleTimeString()
      };
      
      this.messages.push(message);
      
      // 限制消息数量，避免内存溢出
      if (this.messages.length > 1000) {
        this.messages = this.messages.slice(-500);
      }
      
      // 自动滚动到底部
      if (this.autoScroll) {
        this.$nextTick(() => {
          const container = this.$refs.messagesContainer;
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
        });
      }
    },

    clearMessages() {
      this.messages = [];
    }
  },

  beforeDestroy() {
    this.disconnect();
  }
};
</script>

<style scoped>
.mqtt-client {
  max-width: 1000px;
  margin: 0 auto;
}

.status-section,
.subscribe-section,
.publish-section,
.messages-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.connected {
  background-color: #4caf50;
}

.status-dot.connecting {
  background-color: #ff9800;
  animation: pulse 1s infinite;
}

.status-dot.disconnected {
  background-color: #9e9e9e;
}

.status-dot.error {
  background-color: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.input-field,
.textarea-field {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.input-group {
  margin-bottom: 15px;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
}

.messages-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.message-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.message-item.received {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
}

.message-item.sent {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
}

.message-item.system {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}

.message-item.error {
  background-color: #ffebee;
  border-left-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.message-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}
</style>

<style scoped>
.mqtt-client {
  max-width: 1000px;
  margin: 0 auto;
}

.status-section,
.subscribe-section,
.publish-section,
.messages-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

h3 {
  color: #555;
  margin-bottom: 10px;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.connected {
  background-color: #4caf50;
}

.status-dot.connecting {
  background-color: #ff9800;
  animation: pulse 1s infinite;
}

.status-dot.disconnected {
  background-color: #9e9e9e;
}

.status-dot.error {
  background-color: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: bold;
  color: #333;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.input-field,
.textarea-field {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.input-field:focus,
.textarea-field:focus {
  outline: none;
  border-color: #42b983;
  box-shadow: 0 0 0 2px rgba(66, 185, 131, 0.2);
}

.textarea-field {
  resize: vertical;
  min-height: 60px;
}

/* 主题列表 */
.topic-list {
  max-height: 200px;
  overflow-y: auto;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
}

/* 消息控制 */
.message-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.auto-scroll-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.auto-scroll-label input {
  margin-right: 5px;
}

/* 消息容器 */
.messages-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.message-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.message-item.received {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
}

.message-item.sent {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
}

.message-item.system {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}

.message-item.error {
  background-color: #ffebee;
  border-left-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.message-type {
  font-weight: bold;
}

.message-content {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

/* 连接操作按钮 */
.connection-actions {
  display: flex;
  gap: 10px;
}

/* 发布表单 */
.publish-form {
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mqtt-client {
    padding: 10px;
  }

  .status-section,
  .subscribe-section,
  .publish-section,
  .messages-section {
    padding: 15px;
  }

  .connection-actions {
    flex-direction: column;
  }

  .btn {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .message-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
