19:18:15
SYSTEM
主题: 系统消息
消息: 尝试连接: ws://**************:38084/mgtt
19:18:15
SYSTEM
主题: 系统消息
消息: 使用认证: admin
19:18:15
ERROR
主题: 连接状态
消息: 客户端离线{
  "name": "vue2-mqtt-example",
  "version": "1.0.0",
  "description": "Vue 2 MQTT communication example",
  "main": "src/main.js",
  "scripts": {
    "serve": "vue-cli-service serve",
    "build": "vue-cli-service build",
    "dev": "vue-cli-service serve"
  },
  "dependencies": {
    "mqtt": "^4.3.7",
    "vue": "^2.7.14"
  },
  "devDependencies": {
    "@vue/cli-service": "^5.0.8",
    "buffer": "^6.0.3",
    "crypto-browserify": "^3.12.1",
    "path-browserify": "^1.0.1",
    "process": "^0.11.10",
    "stream-browserify": "^3.0.0",
    "url": "^0.11.4",
    "util": "^0.12.5",
    "vue-template-compiler": "^2.7.14"
  },
  "keywords": [
    "vue",
    "mqtt",
    "websocket",
    "iot"
  ],
  "author": "",
  "license": "MIT"
}
