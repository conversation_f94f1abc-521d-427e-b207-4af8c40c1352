{"name": "vue2-mqtt-example", "version": "1.0.0", "description": "Vue 2 MQTT communication example", "main": "src/main.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "dev": "vue-cli-service serve", "proxy": "node mqtt-proxy-server.js", "dev-with-proxy": "concurrently \"npm run proxy\" \"npm run serve\""}, "dependencies": {"mqtt": "^4.3.7", "vue": "^2.7.14"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "buffer": "^6.0.3", "concurrently": "^7.6.0", "crypto-browserify": "^3.12.1", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "url": "^0.11.4", "util": "^0.12.5", "vue-template-compiler": "^2.7.14", "ws": "^8.18.2"}, "keywords": ["vue", "mqtt", "websocket", "iot"], "author": "", "license": "MIT"}