{"name": "vue2-mqtt-example", "version": "1.0.0", "description": "Vue 2 MQTT communication example", "main": "src/main.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "serve": "http-server dist -p 8080"}, "dependencies": {"mqtt": "^4.3.7", "vue": "^2.7.14"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "http-server": "^14.1.1", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "style-loader": "^3.3.0", "url": "^0.11.4", "util": "^0.12.5", "vue-loader": "^15.10.0", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.7.14", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "keywords": ["vue", "mqtt", "websocket", "iot"], "author": "", "license": "MIT"}