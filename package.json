{"name": "vue2-mqtt-example", "version": "1.0.0", "description": "Vue 2 MQTT communication example", "main": "src/main.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "dev": "vue-cli-service serve"}, "dependencies": {"mqtt": "^4.3.7", "vue": "^2.7.14"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "vue-template-compiler": "^2.7.14"}, "keywords": ["vue", "mqtt", "websocket", "iot"], "author": "", "license": "MIT"}