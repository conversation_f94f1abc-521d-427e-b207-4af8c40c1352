const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    open: true,
    hot: true,
    // WebSocket 代理配置
    proxy: {
      '/mqtt-proxy': {
        target: 'ws://121.201.109.17:38084',
        ws: true, // 启用 WebSocket 代理
        changeOrigin: true,
        pathRewrite: {
          '^/mqtt-proxy': '/mgtt' // 将 /mqtt-proxy 重写为 /mgtt
        },
        logLevel: 'debug'
      }
    }
  },

  // Webpack 配置
  configureWebpack: {
    resolve: {
      fallback: {
        "url": require.resolve("url/"),
        "buffer": require.resolve("buffer/"),
        "process": require.resolve("process/browser"),
        "stream": require.resolve("stream-browserify"),
        "util": require.resolve("util/"),
        "crypto": require.resolve("crypto-browserify"),
        "path": require.resolve("path-browserify"),
        "fs": false,
        "net": false,
        "tls": false
      }
    },
    plugins: [
      new (require('webpack')).ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer']
      })
    ]
  }
})
