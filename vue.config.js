const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,

  devServer: {
    port: 8080,
    open: true
    // 注释掉代理配置，因为 MQTT 服务器不支持 HTTP 代理
    // 我们将直接使用 WebSocket 连接
  },

  // 为 MQTT 库配置 Node.js polyfills
  configureWebpack: {
    resolve: {
      fallback: {
        "buffer": require.resolve("buffer/"),
        "process": require.resolve("process/browser"),
        "stream": require.resolve("stream-browserify"),
        "util": require.resolve("util/"),
        "crypto": require.resolve("crypto-browserify"),
        "path": require.resolve("path-browserify"),
        "url": require.resolve("url/"),
        "fs": false,
        "net": false,
        "tls": false
      }
    },
    plugins: [
      new (require('webpack')).ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer']
      })
    ]
  }
})
