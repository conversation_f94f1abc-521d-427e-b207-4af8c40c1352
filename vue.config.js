const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,

  devServer: {
    port: 8080,
    open: true,
    // MQTT WebSocket 代理 - 解决跨域问题
    proxy: {
      '/mqtt': {
        target: 'ws://121.201.109.17:38084',
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/mqtt': '/mgtt'
        }
      }
    }
  },

  // 为 MQTT 库配置 Node.js polyfills
  configureWebpack: {
    resolve: {
      fallback: {
        "buffer": require.resolve("buffer/"),
        "process": require.resolve("process/browser"),
        "stream": require.resolve("stream-browserify"),
        "util": require.resolve("util/"),
        "crypto": require.resolve("crypto-browserify"),
        "path": require.resolve("path-browserify"),
        "url": require.resolve("url/"),
        "fs": false,
        "net": false,
        "tls": false
      }
    },
    plugins: [
      new (require('webpack')).ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer']
      })
    ]
  }
})
